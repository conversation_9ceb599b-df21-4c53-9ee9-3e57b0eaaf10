import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import { defineConfig, loadEnv, ConfigEnv } from 'vite';
import type * as esbuild from 'esbuild-wasm';
import vueSetupExtend from 'vite-plugin-vue-setup-extend-plus';
import viteCompression from 'vite-plugin-compression';

const pathResolve = (dir: string) => {
    return resolve(__dirname, '.', dir);
};

const alias: Record<string, string> = {
    '/@': pathResolve('./src/'),
    '@': pathResolve('./src/'),
    'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js',
};

const viteConfig = defineConfig((mode: ConfigEnv) => {
    const env = loadEnv(mode.mode, process.cwd());
    return {
        plugins: [vue(), vueSetupExtend(), viteCompression()],
        root: process.cwd(),
        resolve: { alias },
        base: mode.command === 'serve' ? './' : env.VITE_PUBLIC_PATH,
        optimizeDeps: {
            exclude: ['vue-demi', 'echarts-gl'], // 排除 echarts-gl 预构建，避免重复注册警告
            esbuildOptions: {
                loader: {
                    '.js': 'jsx'
                }
            }
        },
        server: {
            host: '0.0.0.0',
            port: env.VITE_PORT as unknown as number,
            open: JSON.parse(env.VITE_OPEN),
            hmr: {
                overlay: false
            },
            proxy: {
                '/dev-api': {
                    target: 'http://*************:8080/',
                    ws: true,
                    changeOrigin: true,
                    rewrite: (path) => path.replace(/^\/dev-api/, ''),
                },
                '/gitee': {
                    target: 'https://gitee.com',
                    ws: true,
                    changeOrigin: true,
                    rewrite: (path) => path.replace(/^\/gitee/, ''),
                },
                '/prod-api': {
                    target: 'http://*************:8091/',
                    ws: true,
                    changeOrigin: true,
                },
                '/fastapi': {
                    target: 'http://localhost:8000',
                    ws: true,
                    changeOrigin: true,
                    rewrite: (path) => path.replace(/^\/fastapi/, ''),
                },
            },
        },
        build: {
            outDir: 'dist',
            chunkSizeWarningLimit: 1500,
            rollupOptions: {
                external: [],
                output: {
                    chunkFileNames: 'assets/js/[name]-[hash].js',
                    entryFileNames: 'assets/js/[name]-[hash].js',
                    assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
                    manualChunks(id) {
                        if (id.includes('node_modules')) {
                            return id.toString().match(/\/node_modules\/(?!.pnpm)(?<moduleName>[^\/]*)\//)?.groups!.moduleName ?? 'vender';
                        }
                    },
                }
            },
        },
        css: {
            preprocessorOptions: {
                css: { charset: false },
                scss: {
                    api: 'modern-compiler' // 使用现代编译器API
                }
            }
        },
        define: {
            __VUE_I18N_LEGACY_API__: JSON.stringify(false),
            __VUE_I18N_FULL_INSTALL__: JSON.stringify(false),
            __INTLIFY_PROD_DEVTOOLS__: JSON.stringify(false),
            __NEXT_VERSION__: JSON.stringify(process.env.npm_package_version),
            __NEXT_NAME__: JSON.stringify(process.env.npm_package_name),
            __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: true,
        },
    };
});

export default viteConfig;
